[{"control_id": "2", "domain": "Information Security Policy", "control": "Are your IT Security Policies, Standards, and Procedures maintained and reviewed at least on an annual basis, and at what level are they approved? ", "evidence_files_considered": ["Screenshot 2025-06-07 113139.png", "Screenshot 2025-06-07 113157.png", "Screenshot 2025-06-07 113213.png", "Screenshot 2025-06-07 113229.png", "Screenshot 2025-06-07 113241.png", "Screenshot 2025-06-07 113252.png", "Screenshot 2025-06-07 113301.png", "Screenshot 2025-06-07 113315.png", "Screenshot 2025-06-07 113325.png", "Screenshot 2025-06-07 113338.png", "Screenshot 2025-06-07 113349.png", "Screenshot 2025-06-07 113402.png", "Screenshot 2025-06-07 113413.png", "Screenshot 2025-06-07 113426.png", "Screenshot 2025-06-07 113438.png", "Screenshot 2025-06-07 113449.png", "Screenshot 2025-06-07 113500.png", "Screenshot 2025-06-07 113511.png", "Screenshot 2025-06-07 113521.png", "Screenshot 2025-06-07 113531.png", "Screenshot 2025-06-07 113541.png", "Screenshot 2025-06-07 113550.png", "Screenshot 2025-06-07 113602.png", "Screenshot 2025-06-07 113616.png", "Screenshot 2025-06-07 113628.png", "Screenshot 2025-06-07 113638.png", "Screenshot 2025-06-07 113650.png", "Screenshot 2025-06-07 113659.png"]}, {"control_id": "5", "domain": "Information Security Organisation", "control": "Are security roles and responsibilities of constituents defined and documented in accordance with the Third Party's information security policy?", "evidence_files_considered": ["2.1 ISO Structure-11-06-25.png", "12.2.1.Security Awareness Training-11-06-25.png", "13.1 Risk Assessment Policy-11-06-2025.png", "13.1 Risk Assessment-11-06-25.png"]}, {"control_id": "7", "domain": "Information Security Organisation", "control": "Is there a security awareness training program? Does it include a mandatory training session for new hires?", "evidence_files_considered": ["2.1 ISO Structure-11-06-25.png", "12.2.1.Security Awareness Training-11-06-25.png", "13.1 Risk Assessment Policy-11-06-2025.png", "13.1 Risk Assessment-11-06-25.png"]}, {"control_id": "9", "domain": "Information Security Organisation", "control": "Is there a risk assessment program that has been approved by management and communicated to appropriate constituents?", "evidence_files_considered": ["2.1 ISO Structure-11-06-25.png", "12.2.1.Security Awareness Training-11-06-25.png", "13.1 Risk Assessment Policy-11-06-2025.png", "13.1 Risk Assessment-11-06-25.png"]}, {"control_id": "10", "domain": "Human Resources Security (Information Security)", "control": "Have you established a background investigation policy that includes which background verification checks should be carried out in accordance with industry best practices and relevant laws and international restrictions?", "evidence_files_considered": ["12.1.1.Personal Screening Policy-11-06-25.png", "12.1.2.Confidentiality Agreement-11-06-25.png", "12.3 Disciplinary Process-11-06-25.png"]}, {"control_id": "11", "domain": "Human Resources Security (Information Security)", "control": "Are new hires required to sign any agreements upon hire? If yes, does it include:\n\n1. Acceptable Use?\n2. Code of Conduct/Ethics?\n3. Non-Disclosure Agreement?\n4. Confidentiality Agreement?\n\nPlease provide details.", "evidence_files_considered": ["12.1.1.Personal Screening Policy-11-06-25.png", "12.1.2.Confidentiality Agreement-11-06-25.png", "12.3 Disciplinary Process-11-06-25.png"]}, {"control_id": "12", "domain": "Human Resources Security (Information Security)", "control": "Is there a formal disciplinary process for employees who have committed a security breach?", "evidence_files_considered": ["12.1.1.Personal Screening Policy-11-06-25.png", "12.1.2.Confidentiality Agreement-11-06-25.png", "12.3 Disciplinary Process-11-06-25.png"]}, {"control_id": "13", "domain": "Asset and Data Management", "control": "Do you have an asset management policy or program that has been approved by management to maintain inventory of hardware, software, information assets (e.g., databases) and physical assets? ", "evidence_files_considered": ["3-1Acceptable Usage Policy-11-06-25.png", "3-2Acceptable usage policy-11-06-25.png", "5.1Inforamtion Classification-11-06-25.png", "5.2Information Classification-11-06-25.png", "5.3Information Classification-11-06-25.png", "5.4 Information classification-11-06-25.png", "5Information classification-11-06-25.png", "3.5.Software Installation Policy-11-06-25.png", "3.Acceptable usage policy-11-06-25.png"]}, {"control_id": "14", "domain": "Asset and Data Management", "control": "Do you have an internal data classification policy? If yes, are there appropriate controls in place in accordance with the classification of data?", "evidence_files_considered": ["3-1Acceptable Usage Policy-11-06-25.png", "3-2Acceptable usage policy-11-06-25.png", "5.1Inforamtion Classification-11-06-25.png", "5.2Information Classification-11-06-25.png", "5.3Information Classification-11-06-25.png", "5.4 Information classification-11-06-25.png", "5Information classification-11-06-25.png", "3.5.Software Installation Policy-11-06-25.png", "3.Acceptable usage policy-11-06-25.png"]}, {"control_id": "15", "domain": "Asset and Data Management", "control": "Do you prevent the use of unauthorized, unlicensed and unsupported hardware/software?", "evidence_files_considered": ["3-1Acceptable Usage Policy-11-06-25.png", "3-2Acceptable usage policy-11-06-25.png", "5.1Inforamtion Classification-11-06-25.png", "5.2Information Classification-11-06-25.png", "5.3Information Classification-11-06-25.png", "5.4 Information classification-11-06-25.png", "5Information classification-11-06-25.png", "3.5.Software Installation Policy-11-06-25.png", "3.Acceptable usage policy-11-06-25.png"]}, {"control_id": "19", "domain": "Access Control", "control": "Is there an Access Control Policy in place? If yes, does the policy outline the  principle of least privilege and need to know basis?", "evidence_files_considered": ["4.2Password Policy-11-06-25.png", "4.Access Control-1-11-06-25.png", "4.Access Control-2-11-06-25.png", "4.Access Control-3-11-06-25.png", "4-Access Control-11-06-25.png"]}, {"control_id": "21", "domain": "Access Control", "control": "Is there a password policy in place? Please share the minimum password requirements that you have implemented.", "evidence_files_considered": ["5.4 Encryption Policy-11-06-25.png"]}, {"control_id": "21", "domain": "Cryptography", "control": "Do you encrypt Client data at rest (on disk/storage) within your environment? What kind of encryption method use to protect data?", "evidence_files_considered": ["5.4 Encryption Policy-11-06-25.png"]}, {"control_id": "22", "domain": "Physical and Environmental Security of IT Facilities", "control": "Do you have a Physical Security Policy in place?", "evidence_files_considered": ["6.1.2.4Physical and Environmental Security.png", "6.Physical and Environmental Security.png", "6.Physical and Environmental Security-1.png"]}, {"control_id": "23", "domain": "Physical and Environmental Security of IT Facilities", "control": "Do you have general physical security controls (other than to restricted areas) in place? \nIf yes, please describe the controls which are in-place", "evidence_files_considered": ["6.1.2.4Physical and Environmental Security.png", "6.Physical and Environmental Security.png", "6.Physical and Environmental Security-1.png"]}, {"control_id": "24", "domain": "Physical and Environmental Security of IT Facilities", "control": "Are there controls in place to secure equipment from environmental threats and hazards, and opportunities for unauthorized access?", "evidence_files_considered": ["6.1.2.4Physical and Environmental Security.png", "6.Physical and Environmental Security.png", "6.Physical and Environmental Security-1.png"]}, {"control_id": "26", "domain": "Operations Security", "control": "Do you have change management policies in place? ", "evidence_files_considered": ["4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "7.1 Change Management.png", "7.1 Change Management-1.png", "7.2 Technical Vulnerability Management.png", "9.2 data breach response.png"]}, {"control_id": "27", "domain": "Operations Security", "control": "Do you have Change Control process or procedures to implement changes for your infrastructures/systems or Services? Please provide in details.", "evidence_files_considered": ["4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "7.1 Change Management.png", "7.1 Change Management-1.png", "7.2 Technical Vulnerability Management.png", "9.2 data breach response.png"]}, {"control_id": "28", "domain": "Operations Security", "control": "Do you detect vulnerabilities within organizationally-owned or managed applications, infrastructure network and system components (e.g. network vulnerability assessment, penetration testing) to ensure the efficiency of implemented security controls?\n\nIf yes, please describe the vulnerability patch management process.", "evidence_files_considered": ["4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "7.1 Change Management.png", "7.1 Change Management-1.png", "7.2 Technical Vulnerability Management.png", "9.2 data breach response.png"]}, {"control_id": "30", "domain": "Operations Security", "control": "Are there operational procedures for system administration consistent with security industry recommendations to ensure least privilege, segregation of duties, and accountability? Are these operational procedures documented, communicated, and adhered by the staff?", "evidence_files_considered": ["4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "7.1 Change Management.png", "7.1 Change Management-1.png", "7.2 Technical Vulnerability Management.png", "9.2 data breach response.png"]}, {"control_id": "31", "domain": "Operations Security – Data Protect", "control": "Do you have a Data Leakage Prevention Policy in place in your organization? ", "evidence_files_considered": ["4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "7.1 Change Management.png", "7.1 Change Management-1.png", "7.2 Technical Vulnerability Management.png", "9.2 data breach response.png"]}, {"control_id": "32", "domain": "Data Communications Security", "control": "Will data be exchanged as part of the relationship with <PERSON><PERSON>? \nIf yes, please describe how is data secured while in transit?", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "33", "domain": "Data Communications Security", "control": "Is Intrusion Protection (IPS) or Intrusion Detection (IDS) Systems deployed to protect the systems or services being provided? ", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "34", "domain": "Data Communications Security", "control": "Do you have security hardening baselines, based on Third Party or other security industry recommendations, to minimize risk to systems and connected networks? ", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "38", "domain": "Data Communications Security", "control": "Do you prohibit employees/contractors from accessing \n-public external e-mail servers\n-external/public instant messaging\n-social media tools\n\nIf not, provide sufficient business justification for the same. ", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "41", "domain": "Data Communications Security – Remote Access Management", "control": "Do you have a remote access control policy? Is it documented and reviewed based on business and information security requirements?", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "42", "domain": "Data Communications Security – Wireless Security", "control": "Do you allow wireless network to connect and access environment storing Client information?", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "43", "domain": "Data Communications Security – Wireless Security", "control": "Is wireless encryption implemented to protect from threats and to maintain security for the systems and applications using the network? If yes, please specify the algorithm (WEP, WPA, WPA2). ", "evidence_files_considered": ["3.Acceptable usage.png", "3.Acceptable usage-1.png", "3.Acceptable usage-2.png", "3.Acceptable usage-3.png", "4.1 Access control policy.png", "4.1 Access control policy-1.png", "4.1 Access control policy-2.png", "11.1-remote access policy.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Threat detection services-1.png", "Threat detection services-2.png"]}, {"control_id": "44", "domain": "System Acquisitions, Development and Maintenance", "control": "Do you use industry standards to build in security for your System/Software Development Lifecycle? In addition to the SDLC, do you have an application security policy in place?", "evidence_files_considered": ["14-Information System Acquisition, Development and Maintenance.png", "14-Information System Acquisition, Development and Maintenance-1.png", "14-Information System Acquisition, Development and Maintenance-2.png", "14-Information System Acquisition, Development and Maintenance-3.png", "Container Scanning-1.png", "Container Scanning-2.png", "Container Scanning-3.png", "Container Scanning-4.png", "Container Scanning-5.png", "Container Scanning-6.png", "Container Scanning-7.png"]}, {"control_id": "48", "domain": "System Acquisitions, Development and Maintenance", "control": "Do you use any source code analysis tool to detect security defects in code prior to production? If yes, what is the tool used?", "evidence_files_considered": ["14-Information System Acquisition, Development and Maintenance.png", "14-Information System Acquisition, Development and Maintenance-1.png", "14-Information System Acquisition, Development and Maintenance-2.png", "14-Information System Acquisition, Development and Maintenance-3.png", "Container Scanning-1.png", "Container Scanning-2.png", "Container Scanning-3.png", "Container Scanning-4.png", "Container Scanning-5.png", "Container Scanning-6.png", "Container Scanning-7.png"]}, {"control_id": "49", "domain": "IT Service and Support by Third Party Vendor (Information Security)", "control": "Do you have a comprehensive third party risk management program in place? If yes, does the program outline the following:\n1. Processes to identify and prioritize third party risks?\n2. Processes to monitor and review third party services?\n3. Processes to manage changes for third party services?", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "50", "domain": "IT Service and Support by Third Party Vendor (Information Security)", "control": "Do you use subcontractors to provide any services related to your Services provided for Client?\n\nIf yes, please list them down along with the brief description of service. ", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "51", "domain": "IT Service and Support by Third Party Vendor (Information Security)", "control": "Is Client data being received, sent, transmitted, stored or processed by these subcontractors?\nIf yes, please describe subcontractors' services and the controls in place to protect Client data.", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "52", "domain": "IT Service and Support by Third Party Vendor – Cloud Security", "control": "Does the Third Party use cloud services for the service provided to Client? If yes please indicate the service:\n-IaaS (Infrastructure-as-a-Service)\n-PaaS (Platform-as-a-Service)\n-SaaS (Software-as-a-Service)\n-or, Storage, Database, Information, Process, Application, Integration, Security, Management, Testing-as-a-service.", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "53", "domain": "IT Service and Support by Third Party Vendor – Cloud Security", "control": "Is there a cloud security policy in your organization? ", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "55", "domain": "IT Service and Support by Third Party Vendor – Cloud Security", "control": "For Client data being accessed, received, processed, hosted, stored by your Services, is Client data is protected from unauthorized access, copying, or modification?\n\nIf yes, please describe the methods and tools used, such as encryption, anonymizing, sanitizing aggregate data.", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "60", "domain": "IT Service and Support by Third Party Vendor – Cloud Security", "control": "Do you encrypt all Client data in transit within the Cloud service? \n\nIf yes, describe the method of transmission and provide details of the encryption (algorithms & strength) utilised.", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "63", "domain": "IT Service and Support by Third Party Vendor – Cloud Security", "control": "Do you have capability to notify Client on any capacity changes made to virtual machines, as the result impact to the pre-agreed performance? \n\nWhat kind of channel will be used to communicate with Client?", "evidence_files_considered": ["5.Information Classification.png", "5.Information Classification-1.png", "5.Information Classification-2.png", "5.Information Classification-3.png", "5.Information Classification-4.png", "5.Information Classification-5.png", "7.1 chnage management.png", "7.1 chnage management-1.png", "8.1 Third party risk.png", "8.1 Third party risk-1.png", "8.1 Third party risk-2.png", "8.2 CSP.png", "8.2 CSP-1.png", "8.2 CSP-2.png", "8.2 CSP-3.png", "Encryption in transit-1.png", "Encryption in transit-2.png", "Encryption in transit-3.png", "Encryption in transit-4.png", "Encryption in transit-5.png", "Encryption in transit-6.png", "Encryption in transit-7.png", "Encryption in transit-8.png"]}, {"control_id": "68", "domain": "Information Security Incident Management", "control": "Do you have a formal privacy incident communication procedure, integrated with the security incident response and escalation procedures to be executed in the event of unauthorized disclosure or breach or other required privacy communication requirement to data subjects or other entities, including applicable law enforcement and governmental agencies. An organization should establish procedures for notification by third parties that access, process or store client-scoped privacy data. These procedures should include the documentation of a post incident report which documents the unauthorized disclosure, breach, lessons learned and a summary of events related to the incident. ", "evidence_files_considered": ["9.2 Data breach.png", "9.2 Data breach-1.png"]}, {"control_id": "70", "domain": "Information Security for Business Continuity and Disaster Recovery", "control": "Do you have business recovery plan/technology recovery plan for the services provided to Client?", "evidence_files_considered": ["10.3 BCP.png", "10.3 BCP-1.png", "10.3 BCP-2.png", "10.3 BCP-3.png", "10.3 BCP-4.png", "10.3 BCP-5.png", "10.3 BCP-6.png", "10.3 BCP-7.png", "10.3 BCP-8.png"]}, {"control_id": "72", "domain": "Information Security for Business Continuity and Disaster Recovery", "control": "Does your company's Business Continuity (BC) plan addresses not only recovery of IT components, but all operations and services, and is it tested at least annually? ", "evidence_files_considered": ["10.3 BCP.png", "10.3 BCP-1.png", "10.3 BCP-2.png", "10.3 BCP-3.png", "10.3 BCP-4.png", "10.3 BCP-5.png", "10.3 BCP-6.png", "10.3 BCP-7.png", "10.3 BCP-8.png"]}, {"control_id": "78", "domain": "Compliance", "control": "Are Personally Identifiable Information (PII) and sensitive data are properly protected/masked and that only authorized individuals have access to it?", "evidence_files_considered": ["Privacy Policy-1.png", "Privacy Policy-2.png", "Privacy Policy-3.png", "Privacy Policy-4.png"]}, {"control_id": "79", "domain": "Compliance", "control": "Do you have policies and procedures around collection and retention of Personally Identifiable Information (PII) and Business Confidential Information? ", "evidence_files_considered": ["Privacy Policy-1.png", "Privacy Policy-2.png", "Privacy Policy-3.png", "Privacy Policy-4.png"]}]