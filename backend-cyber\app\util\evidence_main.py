# imports from packages
from collections import defaultdict
import json
from fastapi import UploadFile
import os
import pandas as pd
from langchain.chains.question_answering import load_qa_chain
import shutil
from multiprocessing.pool import ThreadPool
from concurrent.futures import ThreadPoolExecutor
# These imports are used by the rate_limiter module
# import time
# import random

# custom imports
from app.core.config import configs
from app.core.prompts import prompts
from app.util.utils import (
    get_unique_id,
    create_dir,
    striplines,
)
from app.util.evidence_utils import load_document, local_image_to_data_url
from app.util.decorators import calc_time
from app.util.rate_limiter import rate_limited_api_call, add_delay
from app.util.file_tracker import track_files_for_control
import json
import re
import time


@calc_time
def save_evidence_files(
    files: list[UploadFile],
    start_row: int = 0,
    end_row: int = -1,
    selected_benchmarks: list = None,
    question_method: str = None,
    hardcoded_questions: list = None,
    domain_evidence_mapping: dict = None
) -> list:
    unique_path = None
    try:
        id = get_unique_id()
        print(f"Processing files with ID: {id}")

        unique_path = os.path.join(
            configs.curr_dir,
            configs.TEMP_CONSTANT,
            configs.EVIDENCE_ANALYZER_TEMP_STORAGE,
            id,
        )

        excel_path = os.path.join(unique_path, "excel")
        evidences = os.path.join(unique_path, "evidences")

        create_dir(excel_path)
        create_dir(evidences)

        excel_file = ""

        # Process uploaded files
        print(f"Processing {len(files)} uploaded files")
        for file in files:
            file_name = file.filename
            file_extension = file_name.split(".")[-1].lower()
            print(f"Processing file: {file_name} (extension: {file_extension})")

            if file_extension == "xlsx":
                excel_file = os.path.join(excel_path, file_name)
                with open(excel_file, "wb") as buffer:
                    buffer.write(file.file.read())
                print(f"Excel file saved to: {excel_file}")

            if file_extension in [
                "pdf",
                "docx",
                "doc",
                "txt",
                "jpg",
                "jpeg",
                "png",
            ]:
                path = os.path.join(evidences, file_name)
                with open(path, "wb") as buffer:
                    buffer.write(file.file.read())
                print(f"Evidence file saved to: {path}")

        # If using hardcoded questions and no Excel file was uploaded, create one
        if question_method == 'hardcoded' and not excel_file and hardcoded_questions:
            print(f"Creating Excel file from {len(hardcoded_questions)} hardcoded questions")

            # Extract unique domains from hardcoded questions
            unique_domains = set()
            for q in hardcoded_questions:
                domain = q.get('domain')
                if domain and domain.strip():
                    unique_domains.add(domain)

            print(f"Found {len(unique_domains)} unique domains in hardcoded questions: {unique_domains}")

            # Create a simple Excel file with the hardcoded questions in the new format
            df = pd.DataFrame([
                {
                    'S.No': i+1,
                    'Domain': q.get('domain', 'General') if q.get('domain') and q.get('domain').strip() else 'General',
                    'Control': q.get('control_id', f'Control {i+1}'),
                    'Assessment Questions': q['question']
                } for i, q in enumerate(hardcoded_questions)
            ])
            excel_file = os.path.join(excel_path, 'hardcoded_questions.xlsx')
            df.to_excel(excel_file, index=False)
            print(f"Created hardcoded questions Excel file: {excel_file}")

        if not excel_file:
            print("Error: No Excel file was provided or created")
            raise ValueError("No Excel file was provided or created")

        print(f"Starting analysis with Excel file: {excel_file}")
        result = perform_analysis(
            excel_file_path=excel_file,
            evidence_folder_path=evidences,
            selected_benchmarks=selected_benchmarks,
            start_row=start_row,
            end_row=end_row,
            domain_evidence_mapping=domain_evidence_mapping
        )
        print(f"Analysis completed successfully. Result count: {len(result)}")

        return result
    except Exception as e:
        print(f"Error in save_evidence_files: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # Clean up temporary files
        if unique_path and os.path.exists(unique_path):
            try:
                print(f"Cleaning up temporary files in: {unique_path}")
                shutil.rmtree(unique_path, ignore_errors=True)
            except Exception as e:
                print(f"Error cleaning up temporary files: {str(e)}")


@calc_time
def perform_analysis(excel_file_path: str, evidence_folder_path: str, selected_benchmarks: list = None, start_row: int = 0, end_row: int = -1, domain_evidence_mapping: dict = None):
    try:
        print(f"Reading Excel file: {excel_file_path}")
        questionnare = pd.read_excel(excel_file_path)
        print(f"Excel file read successfully. Rows: {len(questionnare)}")

        # Define possible column names for domain and question fields
        possible_domain_columns = [
            'Domain', 'Security Domain', 'Category', 'Section',
            'Area', 'Control Domain', 'Control Category', 'Topic'
        ]

        possible_question_columns = [
            'Assessment Questions', 'Control', 'Question', 'Description',
            'Requirement', 'Control Objective', 'Control Text', 'Assessment'
        ]

        possible_id_columns = [
            'S.No', '#', 'ID', 'Control ID', 'Requirement ID', 'Number'
        ]

        possible_reference_columns = [
            'Reference', 'Control', 'Standard', 'Framework', 'Source'
        ]

        # Detect column format
        domain_column = None
        question_column = None
        id_column = None
        reference_column = None

        # Check for domain column
        for col in possible_domain_columns:
            if col in questionnare.columns:
                domain_column = col
                break

        # Check for question column
        for col in possible_question_columns:
            if col in questionnare.columns:
                question_column = col
                break

        # Check for ID column
        for col in possible_id_columns:
            if col in questionnare.columns:
                id_column = col
                break

        # Check for reference column
        for col in possible_reference_columns:
            if col in questionnare.columns and col != question_column:
                reference_column = col
                break

        # If we couldn't find the columns, try case-insensitive matching
        if not domain_column:
            for col in questionnare.columns:
                if any(possible.lower() in col.lower() for possible in possible_domain_columns):
                    domain_column = col
                    break

        if not question_column:
            for col in questionnare.columns:
                if any(possible.lower() in col.lower() for possible in possible_question_columns):
                    question_column = col
                    break

        if not id_column:
            for col in questionnare.columns:
                if any(possible.lower() in col.lower() for possible in possible_id_columns):
                    id_column = col
                    break

        if not reference_column:
            for col in questionnare.columns:
                if any(possible.lower() in col.lower() for possible in possible_reference_columns) and col != question_column:
                    reference_column = col
                    break

        # If we still couldn't find the columns, use fallbacks
        if not domain_column:
            # Use the first string column that's not the question, ID, or reference
            for col in questionnare.columns:
                if col != question_column and col != id_column and col != reference_column:
                    if questionnare[col].dtype == 'object':
                        domain_column = col
                        break

        if not question_column:
            # Use the column with the longest text values
            max_length = 0
            for col in questionnare.columns:
                if questionnare[col].dtype == 'object':
                    avg_length = questionnare[col].astype(str).str.len().mean()
                    if avg_length > max_length:
                        max_length = avg_length
                        question_column = col

        if not id_column:
            # Use the first numeric column
            for col in questionnare.columns:
                if questionnare[col].dtype in ['int64', 'float64']:
                    id_column = col
                    break

        if not reference_column and domain_column:
            # Use the domain column as a fallback
            reference_column = domain_column

        # Ensure we have the minimum required columns
        if not domain_column or not question_column or not id_column:
            raise ValueError("Could not identify required columns in the Excel file. Please ensure your file has columns for domain/category, questions/controls, and an ID/number.")

        print(f"Detected columns: Domain='{domain_column}', Question='{question_column}', ID='{id_column}', Reference='{reference_column}'")
        # DEBUG: Print unique domains found in the Excel
        unique_domains = set(str(row[domain_column]).strip().lower() for _, row in questionnare.iterrows() if pd.notna(row[domain_column]) and str(row[domain_column]).strip())
        print(f"[DEBUG] Unique domains found in Excel (normalized): {unique_domains}")

        # Rename columns to match the expected format in the analysis code
        column_mapping = {
            id_column: '#',
            question_column: 'Control',
            domain_column: 'Security Domain'
        }

        if reference_column:
            column_mapping[reference_column] = 'Reference'

        questionnare = questionnare.rename(columns=column_mapping)

        # If Reference column is missing, add it with the same value as Security Domain
        if 'Reference' not in questionnare.columns:
            questionnare['Reference'] = questionnare['Security Domain']

        # Apply row filtering if specified
        if end_row != -1:
            questionnare = questionnare.iloc[start_row:end_row]
        elif start_row > 0:
            questionnare = questionnare.iloc[start_row:]

        # Keep ID column as string to preserve alphanumeric values like "GV.OC-01"
        # Convert to string and strip whitespace
        questionnare['#'] = questionnare['#'].astype(str).str.strip()
        print(f"[DEBUG] Sample control IDs after processing: {questionnare['#'].head().tolist()}")

        # Group questions by domain for domain-based evidence processing
        domain_groups = {}
        # Create a mapping from row index to control ID (S.No)
        index_to_control_id = {}

        for index, row in questionnare.iterrows():
            # Get domain value, use 'General' as fallback if empty
            domain = row['Security Domain']
            if pd.isna(domain) or domain == '':
                domain = 'General'

            # Get the control ID from the S.No column (now renamed to '#')
            # Preserve the original format (e.g., "GV.OC-01", "GV.OC-02") as string
            control_id = str(row['#']).strip()

            # Store the mapping from index to control ID
            index_to_control_id[index] = control_id

            if domain not in domain_groups:
                domain_groups[domain] = []
            domain_groups[domain].append(index)

        print(f"Found {len(domain_groups)} unique domains in the questionnaire")

        # Get evidence details - we'll organize by domain later
        print(f"Getting file groups from: {evidence_folder_path}")
        # DEBUG: Print the domain_evidence_mapping received
        print(f"[DEBUG] domain_evidence_mapping received: {domain_evidence_mapping}")
        print(f"[DEBUG] domain_groups: {domain_groups}")
        print(f"[DEBUG] index_to_control_id mapping: {index_to_control_id}")

        file_groups = group_files_by_domain(
            evidence_path=evidence_folder_path,
            domain_groups=domain_groups,
            domain_evidence_mapping=domain_evidence_mapping,
            index_to_control_id=index_to_control_id
        )
        print(f"File groups created. Total groups: {len(file_groups)}")
        print(f"[DEBUG] File groups keys: {list(file_groups.keys())}")

        # Skip vector DB creation to improve performance
        print("Skipping vector DB creation to improve performance")

        # Initialize evidence cache for reusing loaded evidence files
        evidence_cache = {
            'text': {},  # Will store loaded text documents
            'image': {}  # Will store loaded image data URLs
        }

        # Process questions by domain for better efficiency
        # Collect debug info for evidence mapping
        debug_summary = []
        def debug_collect(row, file_groups):
            # Preserve the original format (e.g., "GV.OC-01", "GV.OC-02") as string
            control_id = str(row["#"]).strip()
            control = row["Control"]
            domain = row["Security Domain"]
            files = []
            if control_id in file_groups:
                files = file_groups[control_id]["text_files"] + file_groups[control_id]["image_files"]
                print(f"[DEBUG] Found files for control ID {control_id} (domain: {domain}): {len(files)} files")
            else:
                print(f"[DEBUG] No files found for control ID {control_id} (domain: {domain})")
            return {
                "control_id": control_id,
                "domain": domain,
                "control": control,
                "evidence_files_considered": files
            }
        for _, row in questionnare.iterrows():
            debug_summary.append(debug_collect(row, file_groups))
        # Write debug summary to file
        debug_path = os.path.join(os.getcwd(), "evidence_debug_summary.json")
        try:
            import json as _json
            with open(debug_path, "w", encoding="utf-8") as f:
                _json.dump(debug_summary, f, indent=2)
            print(f"[DEBUG] Evidence mapping summary written to {debug_path}")
        except Exception as e:
            print(f"[DEBUG] Failed to write evidence mapping summary: {e}")

        response = process_questions_by_domain(questionnare, domain_groups, file_groups, evidence_folder_path, evidence_cache)

        return response
    except Exception as e:
        print(f"Error in perform_analysis: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


@calc_time
def process_questions_by_domain(questionnare: pd.DataFrame, domain_groups: dict, file_groups: dict, evidence_folder_path: str, evidence_cache: dict):
    """
    Process questions grouped by domain for more efficient analysis.
    Uses parallel processing for both domains and questions.

    Args:
        questionnare: DataFrame containing all questions
        domain_groups: Dictionary mapping domains to question indices
        file_groups: Dictionary mapping control IDs to file groups
        evidence_folder_path: Path to the evidence files
        evidence_cache: Cache for loaded evidence files

    Returns:
        List of analysis results for all questions
    """
    response = []

    # Calculate optimal thread count for domain processing
    # Use more threads for domain processing to maximize parallelism
    optimal_domain_thread_count = min(len(domain_groups), os.cpu_count() * 2 or 8)
    print(f"Using {optimal_domain_thread_count} threads for domain processing")

    # Process domains in parallel
    with ThreadPool(processes=optimal_domain_thread_count) as domain_pool:
        domain_results = {}

        # Submit domain processing tasks
        for domain, indices in domain_groups.items():
            domain_results[domain] = domain_pool.apply_async(
                process_single_domain,
                (domain, indices, questionnare, file_groups, evidence_folder_path, evidence_cache)
            )

        # Collect results from all domains
        for domain, result_future in domain_results.items():
            try:
                domain_response = result_future.get()
                if domain_response:
                    response.extend(domain_response)
            except Exception as e:
                print(f"Error processing domain {domain}: {str(e)}")
                import traceback
                traceback.print_exc()

    return response


@calc_time
def process_single_domain(domain, indices, questionnare, file_groups, evidence_folder_path, evidence_cache):
    """
    Process a single domain's questions in parallel.

    Args:
        domain: The domain name
        indices: List of question indices for this domain
        questionnare: DataFrame containing all questions
        file_groups: Dictionary mapping control IDs to file groups
        evidence_folder_path: Path to the evidence files
        evidence_cache: Cache for loaded evidence files

    Returns:
        List of analysis results for this domain's questions
    """
    domain_response = []
    print(f"Processing domain: {domain} with {len(indices)} questions")

    # Get all questions for this domain
    domain_questions = [questionnare.iloc[idx] for idx in indices if not pd.isnull(questionnare.iloc[idx]["#"]) and not pd.isnull(questionnare.iloc[idx]["Control"])]

    if not domain_questions:
        print(f"No valid questions found for domain: {domain}")
        return domain_response

    # Calculate optimal thread pool size based on number of questions
    # Use a smaller thread pool for individual questions to avoid overwhelming the system
    optimal_thread_count = min(len(domain_questions), max(2, configs.thread_pool_size // 2))
    print(f"Using thread pool size of {optimal_thread_count} for domain: {domain}")

    # Process questions in parallel with optimized thread pool
    async_results = {}
    with ThreadPool(processes=optimal_thread_count) as pool:
        for question in domain_questions:
            question_index = question.name
            async_results[question_index] = pool.apply_async(
                analysis_control_row_with_cache,
                (question, file_groups, evidence_folder_path, evidence_cache)
            )

        # Collect results
        for idx, result_future in async_results.items():
            try:
                result = result_future.get()
                if result and len(result) > 0:
                    domain_response.extend(result)
            except Exception as e:
                print(f"Error processing question {idx}: {str(e)}")
                import traceback
                traceback.print_exc()

    return domain_response


@calc_time
def load_evidence_files_batch(file_paths, evidence_folder_path, evidence_cache):
    """
    Load multiple evidence files in a batch.

    Args:
        file_paths: List of file paths to load
        evidence_folder_path: Path to the evidence folder
        evidence_cache: Cache for loaded evidence files

    Returns:
        List of loaded documents
    """
    loaded_documents = []

    # Use ThreadPoolExecutor to load documents in parallel
    with ThreadPoolExecutor(max_workers=min(len(file_paths), 10)) as executor:
        futures = {}

        for file in file_paths:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the document is already in the cache
            if evidence_cache and file_path in evidence_cache['text']:
                print(f"Using cached document for {file}")
                loaded_documents.extend(evidence_cache['text'][file_path])
                continue

            # Submit loading task
            futures[file] = executor.submit(load_document, file_path)

        # Collect results
        for file, future in futures.items():
            try:
                doc_result = future.result()

                # Store the mapping between documents and their source files
                for doc in doc_result:
                    # Add a custom metadata field to track the source file
                    if not hasattr(doc, "metadata"):
                        doc.metadata = {}
                    doc.metadata["source_file"] = file

                # Cache the document if cache is available
                file_path = os.path.join(evidence_folder_path, file)
                if evidence_cache is not None:
                    evidence_cache['text'][file_path] = doc_result

                loaded_documents.extend(doc_result)
            except Exception as e:
                print(f"Error loading document {file}: {str(e)}")

    return loaded_documents


@calc_time
def load_image_files_batch(file_paths, evidence_folder_path, evidence_cache):
    """
    Load multiple image files in a batch.

    Args:
        file_paths: List of file paths to load
        evidence_folder_path: Path to the evidence folder
        evidence_cache: Cache for loaded evidence files

    Returns:
        Tuple of (list of loaded image data URLs, mapping of data URLs to file names)
    """
    loaded_images = []
    image_file_map = {}  # Map to track which data URL corresponds to which file

    # Use ThreadPoolExecutor to load images in parallel
    with ThreadPoolExecutor(max_workers=min(len(file_paths), 10)) as executor:
        futures = {}

        for file in file_paths:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the image is already in the cache
            if evidence_cache and file_path in evidence_cache['image']:
                print(f"Using cached image for {file}")
                data_url = evidence_cache['image'][file_path]
                loaded_images.append(data_url)
                image_file_map[data_url] = file
                continue

            # Submit loading task
            futures[file] = executor.submit(local_image_to_data_url, file_path)

        # Collect results
        for file, future in futures.items():
            try:
                data_url = future.result()

                # Cache the image if cache is available
                file_path = os.path.join(evidence_folder_path, file)
                if evidence_cache is not None:
                    evidence_cache['image'][file_path] = data_url

                loaded_images.append(data_url)
                # Store the mapping between data URLs and their source files
                image_file_map[data_url] = file
            except Exception as e:
                print(f"Error loading image {file}: {str(e)}")

    return loaded_images, image_file_map


@calc_time
def analysis_control_row_with_cache(row: pd.Series, file_groups: dict, evidence_folder_path: str, evidence_cache: dict):
    """
    Analyze a control row with evidence caching.
    This is a wrapper around analysis_control_row that uses the evidence cache.
    """
    # Use the cached evidence if available
    return analysis_control_row(row, file_groups, evidence_folder_path, evidence_cache)


@calc_time
def analysis_control_row(row: pd.Series, file_groups: dict, evidence_folder_path: str, evidence_cache: dict = None):
    # Preserve the original format (e.g., "GV.OC-01", "GV.OC-02") as string
    control_id = str(row["#"]).strip()

    control = row["Control"]

    # Get domain value, use 'General' as fallback if empty
    domain = row["Security Domain"]
    if pd.isna(domain) or domain == '':
        domain = 'General'

    # Get reference value, use domain as fallback if empty
    reference = row["Reference"]
    if pd.isna(reference) or reference == '':
        reference = domain

    result = []

    # Get all available files for this control
    if control_id in file_groups:
        all_text_files = file_groups[control_id]["text_files"]
        all_image_files = file_groups[control_id]["image_files"]
    else:
        # STRICT: If no files for this control's domain, do not use any files (do not fallback to "all")
        all_text_files = []
        all_image_files = []

    all_files = all_text_files + all_image_files

    # DEBUG: Print which evidence files are being considered for this question
    print(f"[DEBUG] Control ID: {control_id} | Domain: {domain} | Control: {control}")
    print(f"[DEBUG] Evidence files considered for this question: {all_files}")

    # Skip if no files are available
    if not all_files:
        # Still identify evidence types even if no files are available
        try:
            evidence_types = identify_evidence_types(domain, control, reference)
            print(f"Identified evidence types for control {control_id} (no files available): {evidence_types}")
        except Exception as e:
            print(f"Error identifying evidence types for control {control_id}: {str(e)}")
            evidence_types = "- Policy document\n- Configuration screenshot\n- Audit log\n- Procedure document"

        json_dict = {
            "observation": "No evidence files were provided for this control.",
            "control_id": control_id,
            "control": control,
            "evidence_files": [],
            "evidence_types": evidence_types,
            "risk": "The absence of evidence files for this control creates a significant risk as compliance cannot be verified.",
            "Recommendation": "Implement and document the required controls, then provide evidence files for assessment.",
            "compliant": "Non Compliant",
            "control_reference": "No specific document references identified.",
        }
        result.append(json_dict)
        return result

    try:
        # Step 1: Identify appropriate evidence types
        print(f"Identifying evidence types for control {control_id}")
        evidence_types = identify_evidence_types(
            domain, control, reference
        )
        print(f"Identified evidence types for control {control_id}: {evidence_types}")

        # Step 2: Select relevant files based on evidence types
        print(f"Selecting relevant files for control {control_id}")
        relevant_files = select_relevant_evidence_files(
            domain, control, reference, all_files, evidence_types
        )

        print(f"Selected {len(relevant_files)} relevant files for control {control_id}: {relevant_files}")

        # If no relevant files, return a special result
        if not relevant_files:
            json_dict = {
                "observation": "No relevant evidence was found for this control.",
                "control_id": control_id,
                "control": control,
                "evidence_files": [],
                "evidence_types": evidence_types,  # Include the identified evidence types
                "risk": "The absence of relevant evidence for this control creates a significant risk as compliance cannot be verified.",
                "Recommendation": "Implement and document the required controls, then provide evidence files for assessment.",
                "compliant": "Non Compliant",
                "control_reference": "No specific document references identified.",
            }
            result.append(json_dict)
            return result

        # Step 2: Process only the relevant files
        relevant_text_files = [f for f in relevant_files if f.split('.')[-1].lower() in ["pdf", "docx", "doc", "txt"]]
        relevant_image_files = [f for f in relevant_files if f.split('.')[-1].lower() in ["jpg", "jpeg", "png"]]

        # Load only the relevant files
        loaded_documents = []
        loaded_images = []

        # Load text documents in batch
        if relevant_text_files:
            loaded_documents = load_evidence_files_batch(relevant_text_files, evidence_folder_path, evidence_cache)

        # Load images in batch
        if relevant_image_files:
            loaded_images, _ = load_image_files_batch(relevant_image_files, evidence_folder_path, evidence_cache)

        # Step 3: Generate observation using the unified prompt
        print(f"Generating observation for control {control_id}")
        observation = generate_unified_observation(
            domain, control, reference, loaded_documents, loaded_images
        )

        # Step 4: Generate risk/recommendation with control reference
        print(f"Generating risk, recommendation, and control reference for control {control_id}")

        # Get risk recommendation response which now includes control reference
        risk_recommendation = get_risk_recommendation_without_embeddings(
            domain, control, reference, observation
        )

        # Parse the risk recommendation response
        try:
            # Try to find the JSON object using regex to handle potential formatting issues
            json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

            if json_match:
                risk_recommendation_json_string = json_match.group(1)
                risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
                risk = risk_recommendation_json_data.get("Risk", "NA")
                recommendation = risk_recommendation_json_data.get("Recommendation", "NA")
                compliant = risk_recommendation_json_data.get("Compliant", "Non Compliant")
                control_reference = risk_recommendation_json_data.get("ControlReference", "No specific document references identified.")
            else:
                # Fallback if no JSON found
                print(f"No JSON found in risk recommendation response: {risk_recommendation[:100]}...")
                risk = "NA - Unable to extract risk information"
                recommendation = "NA - Unable to extract recommendation information"
                compliant = "Non Compliant"
                control_reference = "No specific document references identified."
        except Exception as e:
            # Handle any parsing errors
            print(f"Error parsing risk recommendation JSON: {str(e)}")
            print(f"Response content: {risk_recommendation[:100]}...")
            risk = "NA - Error in risk assessment"
            recommendation = "NA - Error in recommendation generation"
            compliant = "Non Compliant"
            control_reference = "Error extracting document references."

        # Step 5: Return the result
        json_dict = {
            "observation": observation,
            "control_id": control_id,
            "control": control,
            "evidence_files": relevant_files,  # Use the pre-selected relevant files
            "evidence_types": evidence_types,  # Include the identified evidence types
            "risk": risk,
            "Recommendation": recommendation,
            "compliant": compliant,
            "control_reference": control_reference,  # Add the new control reference field
        }
        result.append(json_dict)

    except Exception as e:
        print(f"Error processing control {control_id}: {str(e)}")
        import traceback
        traceback.print_exc()
        # Add a fallback result
        json_dict = {
            "observation": f"Error processing control: {str(e)}",
            "control_id": control_id,
            "control": control,
            "evidence_files": [],
            "evidence_types": "- Error identifying evidence types",
            "risk": "NA",
            "Recommendation": "NA",
            "compliant": "NA",
            "control_reference": "Error processing control reference.",
        }
        result.append(json_dict)

    return result


@calc_time
def group_files_by_domain(evidence_path: str, domain_groups: dict = None, domain_evidence_mapping: dict = None, index_to_control_id: dict = None) -> dict:
    """
    Group files by domain for domain-based evidence processing.

    Args:
        evidence_path: Path to the evidence files
        domain_groups: Dictionary mapping domains to question indices
        domain_evidence_mapping: Dictionary mapping domains to evidence file names
        index_to_control_id: Dictionary mapping row indices to control IDs (S.No values)

    Returns:
        Dictionary mapping control IDs to file groups
    """
    # Get a list of all files in the folder
    all_files = os.listdir(evidence_path)

    # Create a dictionary to store file groups
    file_groups = defaultdict(lambda: {"text_files": [], "image_files": []})

    # Create a dictionary to store domain-specific file groups
    domain_file_groups = defaultdict(lambda: {"text_files": [], "image_files": []})

    # Create a special "all" group that contains all files
    all_group = {"text_files": [], "image_files": []}

    text_file_types = ["pdf", "docx", "doc", "txt"]
    image_file_types = ["jpg", "jpeg", "png"]

    # First, categorize all files by type
    for file_name in all_files:
        file_extension = file_name.split(".")[-1].lower()

        if file_extension in text_file_types:
            all_group["text_files"].append(file_name)
        elif file_extension in image_file_types:
            all_group["image_files"].append(file_name)
        else:
            continue  # Skip files with unsupported extensions

    # Create a special "all" key that will be used for controls without specific files
    file_groups["all"] = {
        "text_files": all_group["text_files"],
        "image_files": all_group["image_files"]
    }

    # If domain evidence mapping is provided, use it to organize files by domain
    if domain_evidence_mapping and domain_groups:
        print(f"Using domain evidence mapping to organize files")
        print(f"Domain evidence mapping: {domain_evidence_mapping.keys()}")
        print(f"Domain groups: {domain_groups.keys()}")
        # DEBUG: Print mapping of files to domains
        for domain, files in domain_evidence_mapping.items():
            print(f"[DEBUG] Domain '{domain}' mapped to files: {files}")

        # For each domain in the evidence mapping, create a file group with only the files for that domain
        for domain, file_names in domain_evidence_mapping.items():
            domain_text_files = []
            domain_image_files = []

            # Filter files by domain
            for file_name in file_names:
                if file_name in all_files:
                    file_extension = file_name.split(".")[-1].lower()
                    if file_extension in text_file_types:
                        domain_text_files.append(file_name)
                    elif file_extension in image_file_types:
                        domain_image_files.append(file_name)

            # Create domain file group
            domain_file_groups[domain] = {
                "text_files": domain_text_files,
                "image_files": domain_image_files
            }

            # Assign domain files to each control in this domain
            if domain in domain_groups:
                for index in domain_groups[domain]:
                    # Use the mapping from index to control ID if available
                    if index_to_control_id and index in index_to_control_id:
                        control_id = index_to_control_id[index]
                        file_groups[control_id] = domain_file_groups[domain]
                        print(f"[DEBUG] Assigned files for domain '{domain}' to control ID '{control_id}' (from index {index})")
                    else:
                        # Fallback: Use the index as a string directly (no integer conversion)
                        control_id = str(index)
                        file_groups[control_id] = domain_file_groups[domain]
                        print(f"[DEBUG] Fallback: Assigned files for domain '{domain}' to control ID '{control_id}' (from index {index})")

        # For domains without specific files, assign empty lists (STRICT: do not use all files)
        for domain, indices in domain_groups.items():
            if domain not in domain_evidence_mapping or (not domain_file_groups[domain]["text_files"] and not domain_file_groups[domain]["image_files"]):
                print(f"No specific files for domain '{domain}', assigning empty evidence lists")
                domain_file_groups[domain] = {
                    "text_files": [],
                    "image_files": []
                }

                # Assign empty lists to each control in this domain
                for index in indices:
                    # Use the mapping from index to control ID if available
                    if index_to_control_id and index in index_to_control_id:
                        control_id = index_to_control_id[index]
                        file_groups[control_id] = domain_file_groups[domain]
                        print(f"[DEBUG] Assigned empty lists for domain '{domain}' to control ID '{control_id}' (from index {index})")
                    else:
                        # Fallback: Use the index as a string directly (no integer conversion)
                        control_id = str(index)
                        file_groups[control_id] = domain_file_groups[domain]
                        print(f"[DEBUG] Fallback: Assigned empty lists for domain '{domain}' to control ID '{control_id}' (from index {index})")

    # If domain groups are provided but no domain evidence mapping, organize files by domain using all files
    elif domain_groups:
        print(f"No domain evidence mapping provided, using all files for each domain")
        print(f"Domain groups: {domain_groups.keys()}")

        # For each domain, assign all files to each control in that domain
        for domain, indices in domain_groups.items():
            # Create domain file group if it doesn't exist
            if domain not in domain_file_groups:
                domain_file_groups[domain] = {
                    "text_files": all_group["text_files"],
                    "image_files": all_group["image_files"]
                }

            # Assign domain files to each control in this domain
            for index in indices:
                # Use the mapping from index to control ID if available
                if index_to_control_id and index in index_to_control_id:
                    control_id = index_to_control_id[index]
                    file_groups[control_id] = domain_file_groups[domain]
                    print(f"[DEBUG] Assigned all files for domain '{domain}' to control ID '{control_id}' (from index {index})")
                else:
                    # Fallback: Use the index as a string directly (no integer conversion)
                    control_id = str(index)
                    file_groups[control_id] = domain_file_groups[domain]
                    print(f"[DEBUG] Fallback: Assigned all files for domain '{domain}' to control ID '{control_id}' (from index {index})")
    else:
        # For backward compatibility, check if any files have control ID prefixes
        for file_name in all_files:
            file_extension = file_name.split(".")[-1].lower()

            # Try to extract prefix if it exists (for backward compatibility)
            parts = file_name.split("_", 1)
            if len(parts) > 1 and parts[0].isdigit():
                control_id = parts[0]

                if file_extension in text_file_types:
                    file_groups[control_id]["text_files"].append(file_name)
                elif file_extension in image_file_types:
                    file_groups[control_id]["image_files"].append(file_name)

        # Now, for each control ID, make sure it has access to all files
        for control_id in file_groups.keys():
            if control_id != "all":
                # Add all text files to this control's text files
                for file_name in all_group["text_files"]:
                    if file_name not in file_groups[control_id]["text_files"]:
                        file_groups[control_id]["text_files"].append(file_name)

                # Add all image files to this control's image files
                for file_name in all_group["image_files"]:
                    if file_name not in file_groups[control_id]["image_files"]:
                        file_groups[control_id]["image_files"].append(file_name)

    return file_groups


@calc_time
def analysis_image_evidence(
    domain, control, reference, file_groups, control_id, evidence_folder_path, evidence_cache=None
):
    loaded_images = []
    image_file_map = {}  # Map to track which data URL corresponds to which file

    # Use all image files if we're using the "all" group
    image_files = file_groups[control_id]["image_files"]

    with ThreadPoolExecutor() as executor:
        for file in image_files:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the image is already in the cache
            if evidence_cache and file_path in evidence_cache['image']:
                print(f"Using cached image for {file}")
                data_url = evidence_cache['image'][file_path]
            else:
                # Load the image and cache it
                data_url = executor.submit(local_image_to_data_url, file_path).result()

                # Cache the image if cache is available
                if evidence_cache is not None:
                    evidence_cache['image'][file_path] = data_url

            loaded_images.append(data_url)
            # Store the mapping between data URLs and their source files
            image_file_map[data_url] = file

    observation_image = get_response_image_evidence(
        domain, control, reference, loaded_images
    )

    # Track which files were referenced in the observation
    referenced_files = track_files_for_control(
        control_id, observation_image, file_groups, evidence_folder_path
    )

    risk_recommendation = get_risk_recommendation(
        domain, control, reference, observation_image
    )

    # More robust JSON extraction
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        import re
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            risk = risk_recommendation_json_data.get("Risk", "NA")
            recommendation = risk_recommendation_json_data.get("Recommendation", "NA")
            compliant = risk_recommendation_json_data.get("Compliant", "Non Compliant")
        else:
            # Fallback if no JSON found
            print(f"No JSON found in risk recommendation response: {risk_recommendation[:100]}...")
            risk = "NA - Unable to extract risk information"
            recommendation = "NA - Unable to extract recommendation information"
            compliant = "Non Compliant"
    except Exception as e:
        # Handle any parsing errors
        print(f"Error parsing risk recommendation JSON: {str(e)}")
        print(f"Response content: {risk_recommendation[:100]}...")
        risk = "NA - Error in risk assessment"
        recommendation = "NA - Error in recommendation generation"
        compliant = "Non Compliant"

    # Extract control reference from the risk recommendation response
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            control_reference = risk_recommendation_json_data.get("ControlReference", "No specific document references identified.")
        else:
            # Fallback if no JSON found
            control_reference = "No specific document references identified."
    except Exception as e:
        # Handle any parsing errors
        print(f"Error extracting control reference: {str(e)}")
        control_reference = "Error extracting document references."

    json_dict = {
        "observation": observation_image,
        "control_id": control_id,
        "control": control,
        "evidence_files": referenced_files,  # Use the tracked files instead of all files
        "risk": risk,
        "Recommendation": recommendation,
        "compliant": compliant,
        "control_reference": control_reference,
    }

    return json_dict


@calc_time
def analysis_image_text_evidence(
    domain, control, reference, file_groups, control_id, evidence_folder_path, evidence_cache=None
):
    loaded_documents = []
    loaded_images = []
    image_file_map = {}  # Map to track which data URL corresponds to which file

    # Use all files if we're using the "all" group
    text_files = file_groups[control_id]["text_files"]
    image_files = file_groups[control_id]["image_files"]

    with ThreadPoolExecutor() as executor:
        # Process text files with caching
        for file in text_files:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the document is already in the cache
            if evidence_cache and file_path in evidence_cache['text']:
                print(f"Using cached document for {file}")
                doc_result = evidence_cache['text'][file_path]
            else:
                # Load the document and cache it
                doc_result = executor.submit(load_document, file_path).result()

                # Store the mapping between documents and their source files
                for doc in doc_result:
                    # Add a custom metadata field to track the source file
                    if not hasattr(doc, "metadata"):
                        doc.metadata = {}
                    doc.metadata["source_file"] = file

                # Cache the document if cache is available
                if evidence_cache is not None:
                    evidence_cache['text'][file_path] = doc_result

            loaded_documents.extend(doc_result)

        # Process image files with caching
        for file in image_files:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the image is already in the cache
            if evidence_cache and file_path in evidence_cache['image']:
                print(f"Using cached image for {file}")
                data_url = evidence_cache['image'][file_path]
            else:
                # Load the image and cache it
                data_url = executor.submit(local_image_to_data_url, file_path).result()

                # Cache the image if cache is available
                if evidence_cache is not None:
                    evidence_cache['image'][file_path] = data_url

            loaded_images.append(data_url)
            # Store the mapping between data URLs and their source files
            image_file_map[data_url] = file

    # Use a smaller thread pool for parallel processing
    # This is because we're already parallelizing at the domain level
    thread_count = min(2, configs.thread_pool_size)
    pool = ThreadPool(processes=thread_count)

    threads = {}

    threads["observation_text"] = pool.apply_async(
        get_response_text_evidence, (domain, control, reference, loaded_documents)
    )
    threads["observation_image"] = pool.apply_async(
        get_response_image_evidence, (domain, control, reference, loaded_images)
    )

    temp_text = threads["observation_text"].get()
    if len(temp_text) > 0:
        observation_text = temp_text

    temp_image = threads["observation_image"].get()
    if len(temp_image) > 0:
        observation_image = temp_image

    summarized_observation = summarize_observations(
        domain, control, reference, observation_text, observation_image
    )

    # Track which files were referenced in the observation
    referenced_files = track_files_for_control(
        control_id, summarized_observation, file_groups, evidence_folder_path
    )

    # If no files were detected in the summarized observation, check the individual observations
    if not referenced_files:
        text_referenced = track_files_for_control(
            control_id, observation_text, file_groups, evidence_folder_path
        )
        image_referenced = track_files_for_control(
            control_id, observation_image, file_groups, evidence_folder_path
        )
        referenced_files = list(set(text_referenced + image_referenced))

    risk_recommendation = get_risk_recommendation(
        domain, control, reference, summarized_observation
    )

    # More robust JSON extraction
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        import re
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            risk = risk_recommendation_json_data.get("Risk", "NA")
            recommendation = risk_recommendation_json_data.get("Recommendation", "NA")
            compliant = risk_recommendation_json_data.get("Compliant", "Non Compliant")
        else:
            # Fallback if no JSON found
            print(f"No JSON found in risk recommendation response: {risk_recommendation[:100]}...")
            risk = "NA - Unable to extract risk information"
            recommendation = "NA - Unable to extract recommendation information"
            compliant = "Non Compliant"
    except Exception as e:
        # Handle any parsing errors
        print(f"Error parsing risk recommendation JSON: {str(e)}")
        print(f"Response content: {risk_recommendation[:100]}...")
        risk = "NA - Error in risk assessment"
        recommendation = "NA - Error in recommendation generation"
        compliant = "Non Compliant"

    # Extract control reference from the risk recommendation response
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            control_reference = risk_recommendation_json_data.get("ControlReference", "No specific document references identified.")
        else:
            # Fallback if no JSON found
            control_reference = "No specific document references identified."
    except Exception as e:
        # Handle any parsing errors
        print(f"Error extracting control reference: {str(e)}")
        control_reference = "Error extracting document references."

    json_dict = {
        "observation": summarized_observation,
        "control_id": control_id,
        "control": control,
        "evidence_files": referenced_files,  # Use the tracked files instead of all files
        "risk": risk,
        "Recommendation": recommendation,
        "compliant": compliant,
        "control_reference": control_reference,
    }

    return json_dict


@calc_time
def analysis_text_evidence(
    domain, control, reference, file_groups, control_id, evidence_folder_path, evidence_cache=None
):
    loaded_documents = []

    # Use all text files if we're using the "all" group
    text_files = file_groups[control_id]["text_files"]

    with ThreadPoolExecutor() as executor:
        for file in text_files:
            file_path = os.path.join(evidence_folder_path, file)

            # Check if the file is already in the cache
            if evidence_cache and file_path in evidence_cache['text']:
                print(f"Using cached document for {file}")
                doc_result = evidence_cache['text'][file_path]
            else:
                # Load the document and cache it
                doc_result = executor.submit(load_document, file_path).result()

                # Store the mapping between documents and their source files
                for doc in doc_result:
                    # Add a custom metadata field to track the source file
                    if not hasattr(doc, "metadata"):
                        doc.metadata = {}
                    doc.metadata["source_file"] = file

                # Cache the document if cache is available
                if evidence_cache is not None:
                    evidence_cache['text'][file_path] = doc_result

            loaded_documents.extend(doc_result)

    observation_text = get_response_text_evidence(
        domain, control, reference, loaded_documents
    )

    # Track which files were referenced in the observation
    referenced_files = track_files_for_control(
        control_id, observation_text, file_groups, evidence_folder_path
    )

    risk_recommendation = get_risk_recommendation(
        domain, control, reference, observation_text
    )

    # More robust JSON extraction
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        import re
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            risk = risk_recommendation_json_data.get("Risk", "NA")
            recommendation = risk_recommendation_json_data.get("Recommendation", "NA")
            compliant = risk_recommendation_json_data.get("Compliant", "Non Compliant")
        else:
            # Fallback if no JSON found
            print(f"No JSON found in risk recommendation response: {risk_recommendation[:100]}...")
            risk = "NA - Unable to extract risk information"
            recommendation = "NA - Unable to extract recommendation information"
            compliant = "Non Compliant"
    except Exception as e:
        # Handle any parsing errors
        print(f"Error parsing risk recommendation JSON: {str(e)}")
        print(f"Response content: {risk_recommendation[:100]}...")
        risk = "NA - Error in risk assessment"
        recommendation = "NA - Error in recommendation generation"
        compliant = "Non Compliant"

    # Extract control reference from the risk recommendation response
    try:
        # Try to find the JSON object using regex to handle potential formatting issues
        json_match = re.search(r'(\{.*?\})', risk_recommendation, re.DOTALL)

        if json_match:
            risk_recommendation_json_string = json_match.group(1)
            risk_recommendation_json_data = json.loads(risk_recommendation_json_string)
            control_reference = risk_recommendation_json_data.get("ControlReference", "No specific document references identified.")
        else:
            # Fallback if no JSON found
            control_reference = "No specific document references identified."
    except Exception as e:
        # Handle any parsing errors
        print(f"Error extracting control reference: {str(e)}")
        control_reference = "Error extracting document references."

    json_dict = {
        "observation": observation_text,
        "control_id": control_id,
        "control": control,
        "evidence_files": referenced_files,  # Use the tracked files instead of all files
        "risk": risk,
        "Recommendation": recommendation,
        "compliant": compliant,
        "control_reference": control_reference,
    }

    return json_dict


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def get_response_text_evidence(domain, control, reference, document):
    context = striplines(
        prompts.text_evidence_prompt.format(
            control=control, domain=domain, reference=reference
        )
    )
    qa_chain = load_qa_chain(
        llm=configs.Azure_LangChain_client_GPT4_Turbo, chain_type="stuff", verbose=False
    )

    # Add a small delay before making the API call to avoid rate limits
    add_delay(0.5)

    response = qa_chain.invoke(
        input={"input_documents": document, "question": context},
        return_only_outputs=True,
    )

    return response["output_text"]


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def get_response_image_evidence(domain, control, reference, image_urls):
    context = striplines(
        prompts.image_evidence_prompt.format(
            domain=domain, control=control, reference=reference
        )
    )
    client = configs.Azure_client_GPT4_Vision

    message = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": [{"type": "text", "text": context}]},
    ]

    for image_url in image_urls:
        message[1]["content"].append(
            {"type": "image_url", "image_url": {"url": image_url}}
        )

    # Add a small delay before making the API call to avoid rate limits
    add_delay(0.5)

    response = client.chat.completions.create(
        model=configs.MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME,
        messages=message,
        max_tokens=2000,  # Explicitly set max tokens
    )

    return response.choices[0].message.content


@calc_time
def summarize_observations(domain, control, reference, observation_1, observation_2):
    context = striplines(
        prompts.summarize_evidence_prompt.format(
            domain=domain, control=control, reference=reference
        )
    )

    user_input = f"""Observation 1 : {observation_1}
                 Observation 2 : {observation_2}"""

    user_input = striplines(user_input)

    client = configs.Azure_client_GPT4_Turbo
    response = client.chat.completions.create(
        model=configs.MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,  # engine = "deployment_name".
        messages=[
            {"role": "system", "content": context},
            {"role": "user", "content": user_input},
        ],
        # max_tokens=2000,
    )

    return response.choices[0].message.content


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def identify_evidence_types(domain, control, reference):
    """
    Identify appropriate evidence types for a specific control.

    Args:
        domain: The domain of the control
        control: The control text
        reference: Reference information for the control

    Returns:
        String containing a list of recommended evidence types
    """
    # Create the prompt
    context = striplines(
        prompts.evidence_type_identification_prompt.format(
            domain=domain,
            control=control,
            reference=reference
        )
    )

    # Call the AI to identify evidence types using client rotation
    client = configs.get_next_azure_client()
    print(f"Using rotated client for evidence type identification")

    # Add retry logic for API calls
    max_retries = 3
    retry_count = 0
    result = None  # Initialize result to None to avoid UnboundLocalError

    while retry_count < max_retries:
        try:
            response = client.chat.completions.create(
                model=configs.MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": context},
                ],
            )

            # Check if we got a valid response
            if not response or not response.choices or not response.choices[0].message or not response.choices[0].message.content:
                print(f"Warning: Received empty response from AI for evidence type identification (attempt {retry_count + 1})")
                retry_count += 1
                # Get a different client for the retry
                client = configs.get_next_azure_client()
                print(f"Switching to next client for retry")
                continue

            result = response.choices[0].message.content.strip()
            break  # Success, exit the loop

        except Exception as e:
            print(f"Error calling AI for evidence type identification (attempt {retry_count + 1}): {str(e)}")
            retry_count += 1
            if retry_count >= max_retries:
                print("Max retries reached, returning default evidence types")
                return "- Policy document\n- Configuration screenshot\n- Audit log\n- Procedure document\n- Technical implementation evidence"
            # Get a different client for the retry
            client = configs.get_next_azure_client()
            print(f"Switching to next client for retry")

    # Check if we have a valid result
    if result is None:
        print("No result from evidence type identification, using default evidence types")
        return "- Policy document\n- Configuration screenshot\n- Audit log\n- Procedure document\n- Technical implementation evidence"

    return result


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def select_relevant_evidence_files(domain, control, reference, all_files, evidence_types=None):
    """
    Select the most relevant evidence files for a specific control.

    Args:
        domain: The domain of the control
        control: The control text
        reference: Reference information for the control
        all_files: List of all available evidence files
        evidence_types: Optional string containing recommended evidence types

    Returns:
        List of the most relevant file names (3-5) for this control
    """
    # Create a list of file descriptions (name and type)
    file_descriptions = []
    for file in all_files:
        file_extension = file.split('.')[-1].lower()
        file_type = "text document" if file_extension in ["pdf", "docx", "doc", "txt"] else "image"
        file_descriptions.append(f"{file} ({file_type})")

    # Create the prompt
    context = striplines(
        prompts.evidence_file_selection_prompt.format(
            domain=domain,
            control=control,
            reference=reference,
            evidence_types=evidence_types or "No specific evidence types identified.",
            file_list="\n".join(file_descriptions)
        )
    )

    # Call the AI to select relevant files using client rotation
    # Get the next available client from the rotation
    client = configs.get_next_azure_client()
    print(f"Using rotated client for file selection")

    # Add retry logic for API calls
    max_retries = 3
    retry_count = 0
    result = None  # Initialize result to None to avoid UnboundLocalError

    while retry_count < max_retries:
        try:
            response = client.chat.completions.create(
                model=configs.MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": context},
                ],
            )

            # Check if we got a valid response
            if not response or not response.choices or not response.choices[0].message or not response.choices[0].message.content:
                print(f"Warning: Received empty response from AI for control selection (attempt {retry_count + 1})")
                retry_count += 1
                # Get a different client for the retry
                client = configs.get_next_azure_client()
                print(f"Switching to next client for retry")
                continue

            result = response.choices[0].message.content.strip()
            break  # Success, exit the loop

        except Exception as e:
            print(f"Error calling AI for file selection (attempt {retry_count + 1}): {str(e)}")
            retry_count += 1
            if retry_count >= max_retries:
                print("Max retries reached, returning empty list")
                return []
            # Get a different client for the retry
            client = configs.get_next_azure_client()
            print(f"Switching to next client for retry")

    # Check if we have a valid result before trying to parse it
    if result is None:
        print("No result to parse, returning empty list")
        return []

    # Parse the JSON response
    try:
        # Try to find the JSON array using regex to handle potential formatting issues
        json_match = re.search(r'(\[.*?\])', result, re.DOTALL)

        if json_match:
            relevant_files = json.loads(json_match.group(1))

            # Validate that all returned files exist in the original list
            validated_files = [file for file in relevant_files if file in all_files]

            # Limit to 5 files maximum
            if len(validated_files) > 5:
                validated_files = validated_files[:5]

            return validated_files
        else:
            # No JSON array found
            print(f"No JSON array found in response: {result[:100]}...")
            return []
    except Exception as e:
        # Handle any parsing errors
        print(f"Error parsing relevant files JSON: {str(e)}")
        try:
            # Only try to print result if it's a string
            if isinstance(result, str):
                print(f"Response content: {result[:100]}...")
            else:
                print(f"Response is not a string: {type(result)}")
        except Exception:
            print("Could not print response content")
        return []


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def generate_unified_observation(domain, control, reference, loaded_documents, loaded_images):
    """
    Generate a unified observation for both text and image evidence.

    Args:
        domain: The domain of the control
        control: The control text
        reference: Reference information for the control
        loaded_documents: List of loaded text documents
        loaded_images: List of loaded image data URLs

    Returns:
        Observation text
    """
    # Create the prompt
    context = striplines(
        prompts.unified_evidence_prompt.format(
            domain=domain,
            control=control,
            reference=reference
        )
    )

    # If we have both text and image evidence
    if loaded_documents and loaded_images:
        # First process text documents
        qa_chain = load_qa_chain(
            llm=configs.Azure_LangChain_client_GPT4_Turbo, chain_type="stuff", verbose=False
        )

        # Add a small delay before making the API call to avoid rate limits
        add_delay(0.5)

        # Get text observation
        text_response = qa_chain.invoke(
            input={"input_documents": loaded_documents, "question": context},
            return_only_outputs=True,
        )
        text_observation = text_response["output_text"]

        # Then process images - use client rotation
        client = configs.get_next_azure_client()
        print(f"Using rotated client for image observation")

        message = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": [{"type": "text", "text": context}]},
        ]

        for image_url in loaded_images:
            message[1]["content"].append(
                {"type": "image_url", "image_url": {"url": image_url}}
            )

        # Get image observation - no delay needed with client rotation
        image_response = client.chat.completions.create(
            model=configs.MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME,
            messages=message,
            max_tokens=4000,
        )
        image_observation = image_response.choices[0].message.content

        # Combine observations
        combined_context = f"""
        You are a Cyber Security Risk Analyst creating an assessment report.

        You have analyzed both text documents and images related to the following control:

        Control Domain: {domain}
        Control Statement: {control}
        Reference Information: {reference}

        You have two separate observations:

        TEXT OBSERVATION:
        {text_observation}

        IMAGE OBSERVATION:
        {image_observation}

        Please provide a single, unified observation that combines insights from both text and image evidence.
        Focus only on factual observations and be specific about which files contain which evidence points.
        Do not include a "RELEVANT_FILES" section in your response.
        """

        # Get combined observation - use client rotation
        client = configs.get_next_azure_client()
        print(f"Using rotated client for combined observation")
        combined_response = client.chat.completions.create(
            model=configs.MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": combined_context},
            ],
        )

        return combined_response.choices[0].message.content

    # If we only have text documents
    elif loaded_documents:
        qa_chain = load_qa_chain(
            llm=configs.Azure_LangChain_client_GPT4_Turbo, chain_type="stuff", verbose=False
        )

        # Add a small delay before making the API call to avoid rate limits
        add_delay(0.5)

        response = qa_chain.invoke(
            input={"input_documents": loaded_documents, "question": context},
            return_only_outputs=True,
        )

        return response["output_text"]

    # If we only have images
    elif loaded_images:
        # Use client rotation
        client = configs.get_next_azure_client()
        print(f"Using rotated client for image-only observation")

        message = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": [{"type": "text", "text": context}]},
        ]

        for image_url in loaded_images:
            message[1]["content"].append(
                {"type": "image_url", "image_url": {"url": image_url}}
            )

        # No delay needed with client rotation
        response = client.chat.completions.create(
            model=configs.MSP_AZURE_GPT4VISION_DEPLOYMENT_NAME,
            messages=message,
            max_tokens=4000,
        )

        return response.choices[0].message.content

    # If we have no evidence
    else:
        return "No relevant evidence was found for this control."


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def get_risk_recommendation_without_embeddings(domain, control, reference, observation):
    """
    Generate risk and recommendation without using embeddings.

    Args:
        domain: The domain of the control
        control: The control text
        reference: Reference information for the control
        observation: The observation text

    Returns:
        JSON string containing risk, recommendation, and compliance status
    """
    # Load policy documents directly
    policy_docs = load_policy_documents()

    # Create the prompt
    context = striplines(
        prompts.risk_prompt.format(
            domain=domain, control=control, reference=reference, observation=observation
        )
    )

    # Use client rotation - no delay needed
    client = configs.get_next_azure_client()
    print(f"Using rotated client for risk and recommendation")

    response = client.chat.completions.create(
        model=configs.MSP_AZURE_GPT4TURBO_DEPLOYMENT_NAME,
        messages=[
            {"role": "system", "content": context},
            {"role": "user", "content": policy_docs},
        ],
    )

    return response.choices[0].message.content





# Global cache for policy documents
POLICY_DOCS_CACHE = None

def load_policy_documents():
    """
    Load all policy documents directly without using embeddings.
    Uses a global cache to avoid reloading documents.

    Returns:
        String containing the content of all policy documents
    """
    global POLICY_DOCS_CACHE

    # Return cached content if available
    if POLICY_DOCS_CACHE is not None:
        print("Using cached policy documents")
        return POLICY_DOCS_CACHE

    print("Loading policy documents (first time)")
    policy_folder = os.path.join(configs.curr_dir, configs.permanent_storage, configs.POLICY_FILE_PATH)
    policy_content = []

    # Use ThreadPoolExecutor to load documents in parallel
    with ThreadPoolExecutor(max_workers=min(10, os.cpu_count() or 4)) as executor:
        futures = {}

        # Submit loading tasks
        for filename in os.listdir(policy_folder):
            if filename.endswith(".pdf") or filename.endswith(".docx") or filename.endswith(".txt"):
                file_path = os.path.join(policy_folder, filename)
                print(f"[PolicyDocLoader] Loading policy document file: {filename} (full path: {file_path})")
                futures[filename] = executor.submit(load_document, file_path)

        # Collect results
        for filename, future in futures.items():
            try:
                docs = future.result()
                print(f"[PolicyDocLoader] Finished loading: {filename} (docs loaded: {len(docs)})")
                for doc in docs:
                    policy_content.append(f"--- From {filename} ---\n{doc.page_content}")
            except Exception as e:
                print(f"Error loading policy document {filename}: {str(e)}")

    # Cache the result
    POLICY_DOCS_CACHE = "\n\n".join(policy_content)

    return POLICY_DOCS_CACHE


@calc_time
@rate_limited_api_call(max_retries=5, initial_delay=2, backoff_factor=2)
def get_risk_recommendation(domain, control, reference, observation):
    """
    Generate risk, recommendation, and control reference using embeddings.

    This is a legacy function that uses LangChain and embeddings.
    The updated risk_prompt now includes the control reference field.

    Args:
        domain: The domain of the control
        control: The control text
        reference: Reference information for the control
        observation: The observation text

    Returns:
        JSON string containing risk, recommendation, compliance status, and control reference
    """
    vector_store = configs.local_faiss_store

    context = striplines(
        prompts.risk_prompt.format(
            domain=domain, control=control, reference=reference, observation=observation
        )
    )

    docs = vector_store.similarity_search(control, k=5)

    # No delay needed with client rotation
    # Note: We're still using the LangChain client here since this is a legacy function
    chain = load_qa_chain(
        llm=configs.Azure_LangChain_client_GPT4_Turbo, chain_type="stuff"
    )
    response = chain.invoke(
        {"input_documents": docs, "question": context}, return_only_outputs=True
    )

    return response["output_text"]
